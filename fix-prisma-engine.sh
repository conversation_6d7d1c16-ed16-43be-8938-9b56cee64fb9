#!/bin/bash

# Prisma 引擎错误修复脚本
# 解决 "Failed to deserialize constructor options" 错误

echo "🔧 修复 Prisma 引擎错误..."
echo ""

# 检查当前目录
if [ ! -f "package.json" ]; then
    echo "❌ 错误: 请在项目根目录运行此脚本"
    exit 1
fi

# 检查 Prisma 配置
if [ ! -f "prisma/schema.prisma" ]; then
    echo "❌ 错误: 未找到 Prisma schema 文件"
    exit 1
fi

echo "📋 当前环境信息:"
echo "   Node.js: $(node --version)"
echo "   npm: $(npm --version)"
echo "   操作系统: $(uname -s) $(uname -m)"
echo ""

# 1. 清理 Prisma 相关文件
echo "🧹 清理 Prisma 缓存和生成文件..."
rm -rf node_modules/.prisma
rm -rf node_modules/@prisma
rm -rf .next
echo "✅ 清理完成"

# 2. 检查 Prisma 版本
echo "📦 检查 Prisma 版本..."
PRISMA_VERSION=$(npm list @prisma/client --depth=0 2>/dev/null | grep @prisma/client | sed 's/.*@prisma\/client@//' | sed 's/ .*//')
PRISMA_CLI_VERSION=$(npm list prisma --depth=0 2>/dev/null | grep prisma | sed 's/.*prisma@//' | sed 's/ .*//')

echo "   @prisma/client: ${PRISMA_VERSION:-未安装}"
echo "   prisma: ${PRISMA_CLI_VERSION:-未安装}"

# 3. 更新 Prisma 到最新稳定版本
echo "🔄 更新 Prisma 到最新稳定版本..."
npm install @prisma/client@latest prisma@latest
if [ $? -ne 0 ]; then
    echo "❌ Prisma 更新失败"
    exit 1
fi
echo "✅ Prisma 更新完成"

# 4. 重新生成 Prisma 客户端
echo "🔨 重新生成 Prisma 客户端..."
npx prisma generate
if [ $? -ne 0 ]; then
    echo "❌ Prisma 客户端生成失败"
    exit 1
fi
echo "✅ Prisma 客户端生成完成"

# 5. 检查数据库连接
echo "🔗 检查数据库连接..."
if npx prisma db pull --preview-feature 2>/dev/null; then
    echo "✅ 数据库连接正常"
else
    echo "⚠️  数据库连接可能有问题，请检查 DATABASE_URL"
fi

# 6. 验证修复
echo "🧪 验证修复..."
node -e "
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();
console.log('✅ Prisma 客户端创建成功');
prisma.\$disconnect();
" 2>/dev/null

if [ $? -eq 0 ]; then
    echo "✅ Prisma 引擎错误已修复"
else
    echo "❌ 问题仍然存在，尝试其他解决方案..."
    
    # 7. 尝试使用特定的引擎版本
    echo "🔧 尝试使用兼容的引擎版本..."
    
    # 设置环境变量强制使用特定引擎
    export PRISMA_QUERY_ENGINE_LIBRARY="/usr/local/lib/node_modules/@prisma/engines/libquery_engine-linux-musl.so.node"
    export PRISMA_QUERY_ENGINE_BINARY="/usr/local/lib/node_modules/@prisma/engines/query-engine-linux-musl"
    
    # 重新生成
    npx prisma generate
    
    # 再次验证
    node -e "
    const { PrismaClient } = require('@prisma/client');
    const prisma = new PrismaClient();
    console.log('✅ Prisma 客户端创建成功');
    prisma.\$disconnect();
    " 2>/dev/null
    
    if [ $? -eq 0 ]; then
        echo "✅ 使用兼容引擎修复成功"
    else
        echo "❌ 仍然失败，需要手动处理"
    fi
fi

echo ""
echo "🎯 修复完成！"
echo ""
echo "📋 如果问题仍然存在，请尝试:"
echo "1. 检查 Node.js 版本 (推荐 18.17+)"
echo "2. 检查 DATABASE_URL 环境变量"
echo "3. 重启应用: npm start"
echo "4. 查看详细错误: RUST_BACKTRACE=1 npm start"
echo ""
echo "🔗 有用的命令:"
echo "   检查 Prisma 状态: npx prisma status"
echo "   查看数据库: npx prisma studio"
echo "   重置数据库: npx prisma db push --force-reset"
