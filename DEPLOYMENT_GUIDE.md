# 🚀 冒险岛情报站 - 完整部署指南

## 📋 目录

1. [开发环境部署](#1-开发环境部署)
2. [生产环境 - Vercel部署](#2-生产环境---vercel部署)
3. [生产环境 - 自建服务器部署（含Nginx）](#3-生产环境---自建服务器部署含nginx)
4. [生产环境 - 自建服务器部署（无Nginx）](#4-生产环境---自建服务器部署无nginx)
5. [故障排查和常见问题](#5-故障排查和常见问题)
6. [安全配置和性能优化](#6-安全配置和性能优化)

---

## 1. 🛠️ 开发环境部署

### 1.1 系统要求

- **Node.js**: 18.17.0 或更高版本
- **npm**: 9.0.0 或更高版本
- **PostgreSQL**: 14.0 或更高版本
- **Redis**: 6.0 或更高版本（可选）
- **Git**: 最新版本

### 1.2 环境准备

#### 安装 Node.js
```bash
# 使用 nvm 安装（推荐）
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash
source ~/.bashrc
nvm install 18
nvm use 18

# 验证安装
node --version  # 应显示 v18.x.x
npm --version   # 应显示 9.x.x
```

#### 安装 PostgreSQL
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install postgresql postgresql-contrib

# macOS (使用 Homebrew)
brew install postgresql
brew services start postgresql

# Windows
# 下载并安装 PostgreSQL 官方安装包
# https://www.postgresql.org/download/windows/
```

#### 安装 Redis（可选）
```bash
# Ubuntu/Debian
sudo apt install redis-server

# macOS
brew install redis
brew services start redis

# Windows
# 使用 WSL 或下载 Windows 版本
```

### 1.3 项目克隆和依赖安装

```bash
# 克隆项目
git clone <your-repository-url>
cd maplestory-info-station

# 安装依赖
npm install

# 验证依赖安装
npm list --depth=0
```

### 1.4 数据库配置

#### 创建数据库
```bash
# 连接到 PostgreSQL
sudo -u postgres psql

# 创建数据库和用户
CREATE DATABASE mxd_info_db;
CREATE USER postgres WITH PASSWORD 'postgres';
GRANT ALL PRIVILEGES ON DATABASE mxd_info_db TO postgres;

# 退出 PostgreSQL
\q
```

#### 配置数据库端口（如果需要）
```bash
# 编辑 PostgreSQL 配置文件
sudo nano /etc/postgresql/14/main/postgresql.conf

# 修改端口（如果需要使用 5433）
port = 5433

# 重启 PostgreSQL
sudo systemctl restart postgresql
```

### 1.5 环境变量配置

创建 `.env.local` 文件：

```bash
# 复制环境变量模板
cp .env.example .env.local
```

编辑 `.env.local` 文件：

```env
# 数据库配置
DATABASE_URL="postgresql://postgres:postgres@localhost:5433/mxd_info_db"
REDIS_URL="redis://localhost:6379"

# NextAuth配置
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="dev-secret-key-at-least-32-characters-long-for-security"

# 邮件服务 (Resend) - 开发环境可以使用测试密钥
RESEND_API_KEY="re_your-resend-api-key"
EMAIL_FROM="dev@localhost"

# 第三方服务 - 开发环境可以使用测试密钥
NEXT_PUBLIC_FINGERPRINT_JS_API_KEY="your-fingerprint-js-api-key"
STRIPE_SECRET_KEY="sk_test_your-stripe-test-key"
STRIPE_WEBHOOK_SECRET="whsec_your-webhook-secret"

# 安全配置
JWT_SECRET="dev-jwt-secret-at-least-32-characters-long-for-security"
ENCRYPTION_KEY="dev-encryption-key-at-least-32-characters-long-for-security"

# 应用配置
NEXT_PUBLIC_APP_URL="http://localhost:3000"
NEXT_PUBLIC_APP_NAME="冒险岛情报站"

# 开发配置
NODE_ENV="development"
LOG_LEVEL="debug"

# 虚拟货币配置
CURRENCY_NAME="欢乐豆"
DEFAULT_CURRENCY_AMOUNT="100"

# 功能开关
ENABLE_REGISTRATION="true"
ENABLE_EMAIL_VERIFICATION="true"
ENABLE_FINGERPRINT_TRACKING="true"
```

### 1.6 数据库初始化

```bash
# 生成 Prisma 客户端
npx prisma generate

# 推送数据库模式
npx prisma db push

# 运行种子数据
npx prisma db seed

# 验证数据库连接
npx prisma studio
# 访问 http://localhost:5555 查看数据库内容
```

### 1.7 启动开发服务器

```bash
# 启动开发服务器
npm run dev

# 或者使用详细模式
npm run dev -- --turbo

# 服务器启动后访问
# http://localhost:3000
```

### 1.8 开发环境验证

访问以下页面验证功能：

```bash
# 主页
http://localhost:3000

# 登录页面
http://localhost:3000/login

# 注册页面
http://localhost:3000/register

# 调试页面
http://localhost:3000/debug

# 邮件测试页面
http://localhost:3000/test-email

# 认证测试页面
http://localhost:3000/test-auth
```

### 1.9 常见开发环境问题排查

#### 问题1：数据库连接失败
```bash
# 检查 PostgreSQL 状态
sudo systemctl status postgresql

# 检查端口是否正确
sudo netstat -tlnp | grep 5433

# 测试数据库连接
psql -h localhost -p 5433 -U postgres -d mxd_info_db
```

#### 问题2：Redis 连接失败
```bash
# 检查 Redis 状态
sudo systemctl status redis

# 测试 Redis 连接
redis-cli ping
```

#### 问题3：环境变量未生效
```bash
# 检查环境变量文件
cat .env.local

# 重启开发服务器
# Ctrl+C 停止，然后重新运行 npm run dev
```

#### 问题4：Prisma 相关错误
```bash
# 重新生成 Prisma 客户端
npx prisma generate

# 重置数据库（谨慎使用）
npx prisma migrate reset

# 查看数据库状态
npx prisma db pull
```

---

## 2. ☁️ 生产环境 - Vercel部署

### 2.1 准备工作

#### 代码准备
```bash
# 确保代码已提交到 Git 仓库
git add .
git commit -m "准备部署到 Vercel"
git push origin main
```

#### 构建测试
```bash
# 本地构建测试
npm run build
npm run start

# 检查构建产物
ls -la .next/
```

### 2.2 Vercel 项目配置

#### 创建 vercel.json 配置文件
```json
{
  "framework": "nextjs",
  "buildCommand": "npm run build",
  "devCommand": "npm run dev",
  "installCommand": "npm install",
  "functions": {
    "app/api/**/*.ts": {
      "maxDuration": 30
    }
  },
  "regions": ["hkg1", "sin1"],
  "env": {
    "NODE_ENV": "production"
  },
  "build": {
    "env": {
      "NEXT_PUBLIC_APP_URL": "https://your-domain.vercel.app",
      "NEXT_PUBLIC_APP_NAME": "冒险岛情报站"
    }
  },
  "headers": [
    {
      "source": "/api/(.*)",
      "headers": [
        {
          "key": "Access-Control-Allow-Origin",
          "value": "*"
        },
        {
          "key": "Access-Control-Allow-Methods",
          "value": "GET, POST, PUT, DELETE, OPTIONS"
        },
        {
          "key": "Access-Control-Allow-Headers",
          "value": "Content-Type, Authorization"
        }
      ]
    }
  ]
}
```

### 2.3 外部数据库配置

#### 推荐的数据库服务商

**1. Neon (推荐)**
```bash
# 访问 https://neon.tech
# 创建新项目
# 获取连接字符串，格式如下：
# postgresql://username:<EMAIL>/dbname?sslmode=require
```

**2. Supabase**
```bash
# 访问 https://supabase.com
# 创建新项目
# 在 Settings > Database 获取连接字符串
```

**3. PlanetScale**
```bash
# 访问 https://planetscale.com
# 创建新数据库
# 获取连接字符串
```

#### Redis 服务配置

**推荐使用 Upstash Redis**
```bash
# 访问 https://upstash.com
# 创建 Redis 数据库
# 获取连接字符串：redis://xxx:<EMAIL>:6379
```

### 2.4 Vercel 部署流程

#### 方法1：通过 Vercel Dashboard
```bash
1. 访问 https://vercel.com
2. 点击 "New Project"
3. 导入 Git 仓库
4. 配置项目设置
5. 添加环境变量
6. 点击 "Deploy"
```

#### 方法2：通过 Vercel CLI
```bash
# 安装 Vercel CLI
npm i -g vercel

# 登录 Vercel
vercel login

# 部署项目
vercel

# 部署到生产环境
vercel --prod
```

### 2.5 环境变量配置

在 Vercel Dashboard 中配置以下环境变量：

```env
# 数据库配置
DATABASE_URL=****************************************/dbname?sslmode=require
REDIS_URL=redis://username:password@host:6379

# NextAuth配置
NEXTAUTH_URL=https://your-domain.vercel.app
NEXTAUTH_SECRET=production-secret-key-at-least-32-characters-long

# 邮件服务
RESEND_API_KEY=re_your-production-resend-api-key
EMAIL_FROM=<EMAIL>

# 第三方服务
NEXT_PUBLIC_FINGERPRINT_JS_API_KEY=your-production-fingerprint-js-key
STRIPE_SECRET_KEY=sk_live_your-production-stripe-key
STRIPE_WEBHOOK_SECRET=whsec_your-production-webhook-secret

# 安全配置
JWT_SECRET=production-jwt-secret-at-least-32-characters-long
ENCRYPTION_KEY=production-encryption-key-at-least-32-characters-long

# 应用配置
NEXT_PUBLIC_APP_URL=https://your-domain.vercel.app
NEXT_PUBLIC_APP_NAME=冒险岛情报站

# 生产配置
NODE_ENV=production
LOG_LEVEL=info

# 虚拟货币配置
CURRENCY_NAME=欢乐豆
DEFAULT_CURRENCY_AMOUNT=100

# 功能开关
ENABLE_REGISTRATION=true
ENABLE_EMAIL_VERIFICATION=true
ENABLE_FINGERPRINT_TRACKING=true
```

### 2.6 数据库迁移

#### 生产数据库初始化
```bash
# 在本地连接生产数据库进行迁移
DATABASE_URL="your-production-database-url" npx prisma db push
DATABASE_URL="your-production-database-url" npx prisma db seed

# 或者创建迁移脚本
npx prisma migrate dev --name init
DATABASE_URL="your-production-database-url" npx prisma migrate deploy
```

### 2.7 域名配置和SSL

#### 自定义域名配置
```bash
1. 在 Vercel Dashboard 进入项目设置
2. 点击 "Domains" 标签
3. 添加自定义域名
4. 配置 DNS 记录：
   - Type: CNAME
   - Name: www (或 @)
   - Value: your-project.vercel.app
5. 等待 SSL 证书自动配置
```

### 2.8 监控和日志

#### Vercel Analytics 配置
```bash
# 安装 Vercel Analytics
npm install @vercel/analytics

# 在 app/layout.tsx 中添加
import { Analytics } from '@vercel/analytics/react'

export default function RootLayout({ children }) {
  return (
    <html>
      <body>
        {children}
        <Analytics />
      </body>
    </html>
  )
}
```

#### 日志查看
```bash
# 通过 Vercel CLI 查看日志
vercel logs

# 通过 Dashboard 查看
# 访问项目 > Functions 标签 > 点击函数查看日志
```

---

## 3. 🖥️ 生产环境 - 自建服务器部署（含Nginx）

### 3.1 服务器要求

#### 最低配置
- **CPU**: 2核心
- **内存**: 4GB RAM
- **存储**: 20GB SSD
- **操作系统**: Ubuntu 20.04 LTS 或更高版本
- **网络**: 公网IP地址

#### 推荐配置
- **CPU**: 4核心
- **内存**: 8GB RAM
- **存储**: 40GB SSD
- **操作系统**: Ubuntu 22.04 LTS

### 3.2 服务器初始化

#### 更新系统
```bash
# 更新包列表
sudo apt update && sudo apt upgrade -y

# 安装基础工具
sudo apt install -y curl wget git unzip software-properties-common

# 配置时区
sudo timedatectl set-timezone Asia/Shanghai

# 创建应用用户
sudo adduser --disabled-password --gecos "" maplestory
sudo usermod -aG sudo maplestory
```

#### 配置防火墙
```bash
# 启用 UFW 防火墙
sudo ufw enable

# 允许 SSH
sudo ufw allow ssh

# 允许 HTTP 和 HTTPS
sudo ufw allow 80
sudo ufw allow 443

# 检查防火墙状态
sudo ufw status
```

### 3.3 安装 Node.js

```bash
# 安装 Node.js 18.x
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# 验证安装
node --version
npm --version

# 安装 PM2 全局
sudo npm install -g pm2
```

### 3.4 安装和配置 PostgreSQL

```bash
# 安装 PostgreSQL
sudo apt install -y postgresql postgresql-contrib

# 启动并启用 PostgreSQL
sudo systemctl start postgresql
sudo systemctl enable postgresql

# 配置 PostgreSQL
sudo -u postgres psql

# 在 PostgreSQL 中执行
CREATE DATABASE mxd_info_db;
CREATE USER maplestory WITH PASSWORD 'your-secure-password';
GRANT ALL PRIVILEGES ON DATABASE mxd_info_db TO maplestory;
ALTER USER maplestory CREATEDB;
\q

# 配置 PostgreSQL 允许连接
sudo nano /etc/postgresql/14/main/postgresql.conf
# 修改：listen_addresses = 'localhost'

sudo nano /etc/postgresql/14/main/pg_hba.conf
# 添加：local   all   maplestory   md5

# 重启 PostgreSQL
sudo systemctl restart postgresql
```

### 3.5 安装和配置 Redis

```bash
# 安装 Redis
sudo apt install -y redis-server

# 配置 Redis
sudo nano /etc/redis/redis.conf
# 修改以下配置：
# bind 127.0.0.1
# requirepass your-redis-password
# maxmemory 256mb
# maxmemory-policy allkeys-lru

# 重启 Redis
sudo systemctl restart redis-server
sudo systemctl enable redis-server

# 测试 Redis
redis-cli ping
```

### 3.6 安装和配置 Nginx

```bash
# 安装 Nginx
sudo apt install -y nginx

# 启动并启用 Nginx
sudo systemctl start nginx
sudo systemctl enable nginx

# 创建 Nginx 配置文件
sudo nano /etc/nginx/sites-available/maplestory
```

Nginx 配置文件内容：

```nginx
server {
    listen 80;
    server_name your-domain.com www.your-domain.com;

    # 重定向到 HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com www.your-domain.com;

    # SSL 配置（稍后配置证书）
    ssl_certificate /etc/letsencrypt/live/your-domain.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/your-domain.com/privkey.pem;

    # SSL 安全配置
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;

    # 安全头
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;

    # Gzip 压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;

    # 静态文件缓存
    location /_next/static/ {
        alias /home/<USER>/app/.next/static/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    location /images/ {
        alias /home/<USER>/app/public/images/;
        expires 1y;
        add_header Cache-Control "public";
    }

    # 主应用代理
    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;

        # 超时配置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }

    # 健康检查
    location /health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }
}
```

启用 Nginx 配置：

```bash
# 启用站点配置
sudo ln -s /etc/nginx/sites-available/maplestory /etc/nginx/sites-enabled/

# 删除默认配置
sudo rm /etc/nginx/sites-enabled/default

# 测试 Nginx 配置
sudo nginx -t

# 重启 Nginx
sudo systemctl restart nginx
```

### 3.7 SSL 证书配置（Let's Encrypt）

```bash
# 安装 Certbot
sudo apt install -y certbot python3-certbot-nginx

# 获取 SSL 证书
sudo certbot --nginx -d your-domain.com -d www.your-domain.com

# 测试自动续期
sudo certbot renew --dry-run

# 设置自动续期
sudo crontab -e
# 添加以下行：
# 0 12 * * * /usr/bin/certbot renew --quiet
```

### 3.8 部署应用代码

```bash
# 切换到应用用户
sudo su - maplestory

# 克隆代码
git clone <your-repository-url> app
cd app

# 安装依赖
npm ci --only=production

# 创建生产环境变量文件
nano .env.production
```

生产环境变量配置：

```env
# 数据库配置
DATABASE_URL="postgresql://maplestory:your-secure-password@localhost:5432/mxd_info_db"
REDIS_URL="redis://:your-redis-password@localhost:6379"

# NextAuth配置
NEXTAUTH_URL="https://your-domain.com"
NEXTAUTH_SECRET="production-secret-key-at-least-32-characters-long"

# 邮件服务
RESEND_API_KEY="re_your-production-resend-api-key"
EMAIL_FROM="<EMAIL>"

# 第三方服务
NEXT_PUBLIC_FINGERPRINT_JS_API_KEY="your-production-fingerprint-js-key"
STRIPE_SECRET_KEY="sk_live_your-production-stripe-key"
STRIPE_WEBHOOK_SECRET="whsec_your-production-webhook-secret"

# 安全配置
JWT_SECRET="production-jwt-secret-at-least-32-characters-long"
ENCRYPTION_KEY="production-encryption-key-at-least-32-characters-long"

# 应用配置
NEXT_PUBLIC_APP_URL="https://your-domain.com"
NEXT_PUBLIC_APP_NAME="冒险岛情报站"

# 生产配置
NODE_ENV="production"
LOG_LEVEL="info"

# 虚拟货币配置
CURRENCY_NAME="欢乐豆"
DEFAULT_CURRENCY_AMOUNT="100"

# 功能开关
ENABLE_REGISTRATION="true"
ENABLE_EMAIL_VERIFICATION="true"
ENABLE_FINGERPRINT_TRACKING="true"
```

### 3.9 数据库初始化

```bash
# 生成 Prisma 客户端
npx prisma generate

# 推送数据库模式
npx prisma db push

# 运行种子数据
npx prisma db seed
```

### 3.10 构建和启动应用

```bash
# 构建应用
npm run build

# 创建 PM2 配置文件
nano ecosystem.config.js
```

PM2 配置文件：

```javascript
module.exports = {
  apps: [{
    name: 'maplestory-info-station',
    script: 'npm',
    args: 'start',
    cwd: '/home/<USER>/app',
    instances: 'max',
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'production',
      PORT: 3000
    },
    env_file: '.env.production',
    log_file: '/home/<USER>/logs/app.log',
    out_file: '/home/<USER>/logs/out.log',
    error_file: '/home/<USER>/logs/error.log',
    log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
    merge_logs: true,
    max_memory_restart: '1G',
    node_args: '--max-old-space-size=1024'
  }]
}
```

启动应用：

```bash
# 创建日志目录
mkdir -p /home/<USER>/logs

# 启动应用
pm2 start ecosystem.config.js

# 保存 PM2 配置
pm2 save

# 设置 PM2 开机自启
pm2 startup
sudo env PATH=$PATH:/usr/bin /usr/lib/node_modules/pm2/bin/pm2 startup systemd -u maplestory --hp /home/<USER>

# 查看应用状态
pm2 status
pm2 logs
```

### 3.11 自动化部署脚本

创建部署脚本：

```bash
# 创建部署脚本
nano /home/<USER>/deploy.sh
chmod +x /home/<USER>/deploy.sh
```

部署脚本内容：

```bash
#!/bin/bash

# 部署脚本
set -e

APP_DIR="/home/<USER>/app"
BACKUP_DIR="/home/<USER>/backups"
DATE=$(date +%Y%m%d_%H%M%S)

echo "🚀 开始部署 - $DATE"

# 创建备份目录
mkdir -p $BACKUP_DIR

# 备份当前版本
if [ -d "$APP_DIR" ]; then
    echo "📦 备份当前版本..."
    tar -czf "$BACKUP_DIR/backup_$DATE.tar.gz" -C "$APP_DIR" .
fi

# 进入应用目录
cd $APP_DIR

# 拉取最新代码
echo "📥 拉取最新代码..."
git fetch origin
git reset --hard origin/main

# 安装依赖
echo "📦 安装依赖..."
npm ci --only=production

# 构建应用
echo "🔨 构建应用..."
npm run build

# 数据库迁移
echo "🗄️ 数据库迁移..."
npx prisma generate
npx prisma db push

# 重启应用
echo "🔄 重启应用..."
pm2 restart maplestory-info-station

# 等待应用启动
sleep 10

# 健康检查
echo "🏥 健康检查..."
if curl -f http://localhost:3000/health; then
    echo "✅ 部署成功！"
else
    echo "❌ 健康检查失败，回滚到上一版本..."
    # 这里可以添加回滚逻辑
    exit 1
fi

echo "🎉 部署完成 - $DATE"
```

### 3.12 监控和日志管理

#### 设置日志轮转
```bash
# 创建 logrotate 配置
sudo nano /etc/logrotate.d/maplestory

# 配置内容：
/home/<USER>/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 maplestory maplestory
    postrotate
        pm2 reloadLogs
    endscript
}
```

#### 系统监控脚本
```bash
# 创建监控脚本
nano /home/<USER>/monitor.sh
chmod +x /home/<USER>/monitor.sh
```

监控脚本内容：

```bash
#!/bin/bash

# 监控脚本
LOG_FILE="/home/<USER>/logs/monitor.log"
DATE=$(date '+%Y-%m-%d %H:%M:%S')

# 检查应用状态
if ! pm2 list | grep -q "online"; then
    echo "[$DATE] 应用离线，尝试重启..." >> $LOG_FILE
    pm2 restart all
fi

# 检查磁盘空间
DISK_USAGE=$(df / | awk 'NR==2 {print $5}' | sed 's/%//')
if [ $DISK_USAGE -gt 80 ]; then
    echo "[$DATE] 磁盘空间不足: ${DISK_USAGE}%" >> $LOG_FILE
fi

# 检查内存使用
MEMORY_USAGE=$(free | awk 'NR==2{printf "%.2f", $3*100/$2}')
if (( $(echo "$MEMORY_USAGE > 90" | bc -l) )); then
    echo "[$DATE] 内存使用过高: ${MEMORY_USAGE}%" >> $LOG_FILE
fi
```

设置定时任务：

```bash
# 编辑 crontab
crontab -e

# 添加监控任务
*/5 * * * * /home/<USER>/monitor.sh
0 2 * * * /home/<USER>/backup.sh
```

---

## 4. 🖥️ 生产环境 - 自建服务器部署（无Nginx）

### 4.1 服务器要求

与含Nginx部署相同的服务器要求。

### 4.2 服务器初始化

```bash
# 系统更新和基础工具安装（同3.2节）
sudo apt update && sudo apt upgrade -y
sudo apt install -y curl wget git unzip software-properties-common

# 配置防火墙（直接暴露应用端口）
sudo ufw enable
sudo ufw allow ssh
sudo ufw allow 3000  # Next.js 应用端口
sudo ufw allow 443   # 如果需要 HTTPS
```

### 4.3 安装 Node.js、PostgreSQL、Redis

安装步骤与3.3-3.5节相同。

### 4.4 SSL 证书配置（可选）

如果需要 HTTPS，可以在应用层配置 SSL：

```bash
# 安装 Certbot
sudo apt install -y certbot

# 获取证书（standalone 模式）
sudo certbot certonly --standalone -d your-domain.com

# 证书文件位置
# /etc/letsencrypt/live/your-domain.com/fullchain.pem
# /etc/letsencrypt/live/your-domain.com/privkey.pem
```

### 4.5 应用配置

修改 Next.js 配置以支持自定义端口和 HTTPS：

```javascript
// next.config.mjs
/** @type {import('next').NextConfig} */
const nextConfig = {
  // 其他配置...

  // 自定义服务器配置
  experimental: {
    serverComponentsExternalPackages: ['@prisma/client'],
  },

  // 如果使用 HTTPS
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'Strict-Transport-Security',
            value: 'max-age=31536000; includeSubDomains'
          },
        ],
      },
    ]
  },
}

export default nextConfig
```

### 4.6 创建自定义服务器（如果需要 HTTPS）

```javascript
// server.js
const { createServer } = require('https')
const { parse } = require('url')
const next = require('next')
const fs = require('fs')

const dev = process.env.NODE_ENV !== 'production'
const hostname = 'localhost'
const port = process.env.PORT || 3000

const app = next({ dev, hostname, port })
const handle = app.getRequestHandler()

const httpsOptions = {
  key: fs.readFileSync('/etc/letsencrypt/live/your-domain.com/privkey.pem'),
  cert: fs.readFileSync('/etc/letsencrypt/live/your-domain.com/fullchain.pem'),
}

app.prepare().then(() => {
  createServer(httpsOptions, async (req, res) => {
    try {
      const parsedUrl = parse(req.url, true)
      await handle(req, res, parsedUrl)
    } catch (err) {
      console.error('Error occurred handling', req.url, err)
      res.statusCode = 500
      res.end('internal server error')
    }
  })
  .once('error', (err) => {
    console.error(err)
    process.exit(1)
  })
  .listen(port, () => {
    console.log(`> Ready on https://${hostname}:${port}`)
  })
})
```

### 4.7 PM2 配置（无Nginx）

```javascript
// ecosystem.config.js
module.exports = {
  apps: [{
    name: 'maplestory-info-station',
    script: process.env.USE_HTTPS === 'true' ? 'server.js' : 'npm',
    args: process.env.USE_HTTPS === 'true' ? '' : 'start',
    cwd: '/home/<USER>/app',
    instances: 1, // 无Nginx时建议单实例
    exec_mode: 'fork',
    env: {
      NODE_ENV: 'production',
      PORT: 3000,
      USE_HTTPS: 'false' // 或 'true' 如果使用 HTTPS
    },
    env_file: '.env.production',
    log_file: '/home/<USER>/logs/app.log',
    out_file: '/home/<USER>/logs/out.log',
    error_file: '/home/<USER>/logs/error.log',
    log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
    max_memory_restart: '1G',
    node_args: '--max-old-space-size=1024',

    // 健康检查
    health_check_grace_period: 3000,
    health_check_fatal_exceptions: true
  }]
}
```

### 4.8 环境变量配置

```env
# .env.production
# 基础配置与含Nginx版本相同，但需要注意：

# 应用URL（如果使用自定义端口）
NEXT_PUBLIC_APP_URL="https://your-domain.com:3000"
# 或者如果使用标准端口
NEXT_PUBLIC_APP_URL="https://your-domain.com"

# NextAuth URL
NEXTAUTH_URL="https://your-domain.com:3000"
# 或者
NEXTAUTH_URL="https://your-domain.com"

# 其他配置保持不变...
```

### 4.9 启动和监控

```bash
# 启动应用
pm2 start ecosystem.config.js

# 监控应用
pm2 monit

# 查看日志
pm2 logs

# 重启应用
pm2 restart maplestory-info-station
```

### 4.10 备份策略

```bash
# 创建备份脚本
nano /home/<USER>/backup.sh
chmod +x /home/<USER>/backup.sh
```

备份脚本：

```bash
#!/bin/bash

# 备份脚本
BACKUP_DIR="/home/<USER>/backups"
DATE=$(date +%Y%m%d_%H%M%S)

mkdir -p $BACKUP_DIR

# 备份数据库
pg_dump -h localhost -U maplestory mxd_info_db | gzip > "$BACKUP_DIR/db_backup_$DATE.sql.gz"

# 备份应用文件
tar -czf "$BACKUP_DIR/app_backup_$DATE.tar.gz" -C /home/<USER>

# 备份环境变量
cp /home/<USER>/app/.env.production "$BACKUP_DIR/env_backup_$DATE"

# 清理旧备份（保留30天）
find $BACKUP_DIR -name "*backup*" -mtime +30 -delete

echo "备份完成: $DATE"
```

---

## 5. 🔧 故障排查和常见问题

### 5.1 应用启动问题

#### 问题1：端口被占用
```bash
# 查看端口占用
sudo netstat -tlnp | grep :3000
sudo lsof -i :3000

# 杀死占用进程
sudo kill -9 <PID>
```

#### 问题2：数据库连接失败
```bash
# 检查数据库状态
sudo systemctl status postgresql

# 测试数据库连接
psql -h localhost -U maplestory -d mxd_info_db

# 检查数据库日志
sudo tail -f /var/log/postgresql/postgresql-14-main.log
```

#### 问题3：Redis 连接失败
```bash
# 检查 Redis 状态
sudo systemctl status redis-server

# 测试 Redis 连接
redis-cli -a your-redis-password ping

# 检查 Redis 日志
sudo tail -f /var/log/redis/redis-server.log
```

### 5.2 性能问题

#### 内存泄漏排查
```bash
# 查看应用内存使用
pm2 monit

# 查看系统内存
free -h
top -p $(pgrep -f "maplestory")

# 重启应用释放内存
pm2 restart maplestory-info-station
```

#### 数据库性能优化
```sql
-- 查看慢查询
SELECT query, mean_time, calls
FROM pg_stat_statements
ORDER BY mean_time DESC
LIMIT 10;

-- 查看数据库连接
SELECT * FROM pg_stat_activity;

-- 分析表统计信息
ANALYZE;
```

### 5.3 SSL 证书问题

#### 证书过期
```bash
# 检查证书有效期
sudo certbot certificates

# 手动续期
sudo certbot renew

# 强制续期
sudo certbot renew --force-renewal
```

#### Nginx SSL 配置问题
```bash
# 测试 Nginx 配置
sudo nginx -t

# 重新加载 Nginx
sudo systemctl reload nginx

# 查看 Nginx 错误日志
sudo tail -f /var/log/nginx/error.log
```

### 5.4 日志分析

#### 应用日志
```bash
# PM2 日志
pm2 logs maplestory-info-station

# 实时日志
pm2 logs --lines 100

# 错误日志
pm2 logs --err
```

#### 系统日志
```bash
# 系统日志
sudo journalctl -u nginx -f
sudo journalctl -u postgresql -f

# 查看磁盘空间
df -h

# 查看系统负载
htop
```

---

## 6. 🔒 安全配置和性能优化

### 6.1 安全配置

#### 服务器安全
```bash
# 禁用 root 登录
sudo nano /etc/ssh/sshd_config
# 设置：PermitRootLogin no

# 更改 SSH 端口
# 设置：Port 2222

# 重启 SSH
sudo systemctl restart ssh

# 安装 fail2ban
sudo apt install -y fail2ban

# 配置 fail2ban
sudo nano /etc/fail2ban/jail.local
```

fail2ban 配置：

```ini
[DEFAULT]
bantime = 3600
findtime = 600
maxretry = 5

[sshd]
enabled = true
port = 2222
logpath = /var/log/auth.log

[nginx-http-auth]
enabled = true
filter = nginx-http-auth
logpath = /var/log/nginx/error.log
maxretry = 3
```

#### 应用安全
```bash
# 设置文件权限
chmod 600 /home/<USER>/app/.env.production
chown maplestory:maplestory /home/<USER>/app/.env.production

# 定期更新依赖
npm audit
npm audit fix
```

### 6.2 性能优化

#### 数据库优化
```sql
-- PostgreSQL 配置优化
-- 编辑 /etc/postgresql/14/main/postgresql.conf

-- 内存配置
shared_buffers = 256MB
effective_cache_size = 1GB
work_mem = 4MB

-- 连接配置
max_connections = 100

-- 日志配置
log_min_duration_statement = 1000
```

#### Redis 优化
```bash
# 编辑 Redis 配置
sudo nano /etc/redis/redis.conf

# 内存优化
maxmemory 512mb
maxmemory-policy allkeys-lru

# 持久化配置
save 900 1
save 300 10
save 60 10000
```

#### Nginx 优化
```nginx
# 在 nginx.conf 中添加
worker_processes auto;
worker_connections 1024;

# 缓存配置
proxy_cache_path /var/cache/nginx levels=1:2 keys_zone=my_cache:10m max_size=10g
                 inactive=60m use_temp_path=off;

# 在 server 块中添加
location ~* \.(jpg|jpeg|png|gif|ico|css|js)$ {
    expires 1y;
    add_header Cache-Control "public, immutable";
}
```

#### 应用优化
```javascript
// next.config.mjs 优化配置
const nextConfig = {
  // 压缩
  compress: true,

  // 图片优化
  images: {
    formats: ['image/webp', 'image/avif'],
    minimumCacheTTL: 60,
  },

  // 实验性功能
  experimental: {
    optimizeCss: true,
    optimizePackageImports: ['lucide-react'],
  },
}
```

### 6.3 监控和告警

#### 系统监控
```bash
# 安装监控工具
sudo apt install -y htop iotop nethogs

# 创建监控脚本
nano /home/<USER>/system-monitor.sh
```

监控脚本：

```bash
#!/bin/bash

# 系统监控脚本
ALERT_EMAIL="<EMAIL>"
LOG_FILE="/home/<USER>/logs/system-monitor.log"

# CPU 使用率检查
CPU_USAGE=$(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | awk -F'%' '{print $1}')
if (( $(echo "$CPU_USAGE > 80" | bc -l) )); then
    echo "$(date): CPU 使用率过高: ${CPU_USAGE}%" >> $LOG_FILE
fi

# 磁盘空间检查
DISK_USAGE=$(df / | awk 'NR==2 {print $5}' | sed 's/%//')
if [ $DISK_USAGE -gt 85 ]; then
    echo "$(date): 磁盘空间不足: ${DISK_USAGE}%" >> $LOG_FILE
fi

# 应用健康检查
if ! curl -f http://localhost:3000/health > /dev/null 2>&1; then
    echo "$(date): 应用健康检查失败" >> $LOG_FILE
    pm2 restart maplestory-info-station
fi
```

这个完整的部署指南涵盖了从开发环境到生产环境的所有部署场景，包括详细的配置、故障排查和优化建议。您可以根据具体需求选择合适的部署方式。