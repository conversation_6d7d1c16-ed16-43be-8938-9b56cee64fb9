<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="1ece22aa-3aec-4f80-836f-7a624360157a" name="Changes" comment="部署" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="JavaScript File" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 1
}</component>
  <component name="ProjectId" id="2yaJEHQWr9yVRyWusEqJen3JyGU" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;Jest.星力强化算法测试.防止破坏效果.应该正确应用防止破坏效果.executor&quot;: &quot;Run&quot;,
    &quot;Node.js.build-error-test.js.executor&quot;: &quot;Run&quot;,
    &quot;Node.js.build-test.js.executor&quot;: &quot;Run&quot;,
    &quot;Node.js.final-build-test.js.executor&quot;: &quot;Run&quot;,
    &quot;Node.js.final-build-verification.js.executor&quot;: &quot;Run&quot;,
    &quot;Node.js.gen-json-cms-216.js.executor&quot;: &quot;Run&quot;,
    &quot;Node.js.ssg-build-test.js.executor&quot;: &quot;Run&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;ignore.virus.scanning.warn.message&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;D:/vs-program/zg/mxd/maplestory-info-station&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs.jest.jest_package&quot;: &quot;D:/vs-program/zg/mxd/maplestory-info-station/node_modules/jest&quot;,
    &quot;nodejs_interpreter_path&quot;: &quot;C:/nvm4w/nodejs/node&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;pnpm&quot;,
    &quot;npm.dev.executor&quot;: &quot;Run&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;settings.nodejs&quot;,
    &quot;ts.external.directory.path&quot;: &quot;D:\\vs-program\\zg\\mxd\\maplestory-info-station\\node_modules\\typescript\\lib&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="D:\vs-program\zg\mxd\maplestory-info-station" />
      <recent name="D:\vs-program\zg\mxd\maplestory-info-station\deploy" />
      <recent name="D:\vs-program\zg\mxd\maplestory-info-station\docs\code" />
      <recent name="D:\vs-program\zg\mxd\maplestory-info-station\docs" />
      <recent name="D:\vs-program\zg\mxd\maplestory-info-station\public" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="D:\vs-program\zg\mxd\maplestory-info-station\app\api\items" />
    </key>
  </component>
  <component name="RunManager" selected="npm.dev">
    <configuration name="星力强化算法测试.防止破坏效果.应该正确应用防止破坏效果" type="JavaScriptTestRunnerJest" temporary="true" nameIsGenerated="true">
      <node-interpreter value="project" />
      <jest-package value="$PROJECT_DIR$/node_modules/jest" />
      <working-dir value="$PROJECT_DIR$" />
      <envs />
      <scope-kind value="TEST" />
      <test-file value="$PROJECT_DIR$/__tests__/lib/starforce-algorithm.test.ts" />
      <test-names>
        <test-name value="星力强化算法测试" />
        <test-name value="防止破坏效果" />
        <test-name value="应该正确应用防止破坏效果" />
      </test-names>
      <method v="2" />
    </configuration>
    <configuration name="build-test.js" type="NodeJSConfigurationType" temporary="true" nameIsGenerated="true" path-to-js-file="$PROJECT_DIR$/scripts/build-test.js" working-dir="$PROJECT_DIR$/scripts">
      <method v="2" />
    </configuration>
    <configuration name="final-build-test.js" type="NodeJSConfigurationType" temporary="true" nameIsGenerated="true" path-to-js-file="$PROJECT_DIR$/scripts/final-build-test.js" working-dir="$PROJECT_DIR$/scripts">
      <method v="2" />
    </configuration>
    <configuration name="gen-json-cms-216.js" type="NodeJSConfigurationType" temporary="true" nameIsGenerated="true" path-to-js-file="$PROJECT_DIR$/scripts/gen-json-cms-216.js" working-dir="$PROJECT_DIR$/scripts">
      <method v="2" />
    </configuration>
    <configuration name="ssg-build-test.js" type="NodeJSConfigurationType" temporary="true" nameIsGenerated="true" path-to-js-file="ssg-build-test.js" working-dir="$PROJECT_DIR$/scripts">
      <method v="2" />
    </configuration>
    <configuration name="dev" type="js.build_tools.npm" nameIsGenerated="true">
      <package-json value="$PROJECT_DIR$/package.json" />
      <command value="run" />
      <scripts>
        <script value="dev" />
      </scripts>
      <node-interpreter value="project" />
      <envs />
      <method v="2" />
    </configuration>
    <list>
      <item itemvalue="Jest.星力强化算法测试.防止破坏效果.应该正确应用防止破坏效果" />
      <item itemvalue="Node.js.build-test.js" />
      <item itemvalue="Node.js.final-build-test.js" />
      <item itemvalue="Node.js.gen-json-cms-216.js" />
      <item itemvalue="Node.js.ssg-build-test.js" />
      <item itemvalue="npm.dev" />
    </list>
    <recent_temporary>
      <list>
        <item itemvalue="Jest.星力强化算法测试.防止破坏效果.应该正确应用防止破坏效果" />
        <item itemvalue="Node.js.gen-json-cms-216.js" />
        <item itemvalue="Node.js.ssg-build-test.js" />
        <item itemvalue="Node.js.final-build-test.js" />
        <item itemvalue="Node.js.build-test.js" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-js-predefined-1d06a55b98c1-0b3e54e931b4-JavaScript-WS-241.19416.2" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="1ece22aa-3aec-4f80-836f-7a624360157a" name="Changes" comment="" />
      <created>1750066637701</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1750066637701</updated>
      <workItem from="1750066638850" duration="2423000" />
      <workItem from="1750076390466" duration="1712000" />
      <workItem from="1750078131118" duration="7000" />
      <workItem from="1750078200736" duration="11118000" />
      <workItem from="1750149393338" duration="1545000" />
      <workItem from="1750164969566" duration="1861000" />
      <workItem from="1750168022393" duration="1792000" />
      <workItem from="1750212616022" duration="68000" />
      <workItem from="1750301768778" duration="7869000" />
      <workItem from="1750320081081" duration="21641000" />
      <workItem from="1750378143729" duration="7127000" />
      <workItem from="1750404225786" duration="16696000" />
      <workItem from="1750432807218" duration="8706000" />
      <workItem from="1750479198139" duration="24953000" />
      <workItem from="1750571721228" duration="103000" />
      <workItem from="1750571831241" duration="28713000" />
      <workItem from="1750649220996" duration="28318000" />
      <workItem from="1750725123208" duration="9884000" />
      <workItem from="1750735366429" duration="1770000" />
      <workItem from="1750737300280" duration="652000" />
    </task>
    <task id="LOCAL-00003" summary="12">
      <option name="closed" value="true" />
      <created>1750345686896</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1750345686896</updated>
    </task>
    <task id="LOCAL-00004" summary="最后的修改">
      <option name="closed" value="true" />
      <created>1750350279751</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1750350279751</updated>
    </task>
    <task id="LOCAL-00005" summary="最后的修改">
      <option name="closed" value="true" />
      <created>1750379322233</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1750379322233</updated>
    </task>
    <task id="LOCAL-00006" summary="最后的修改">
      <option name="closed" value="true" />
      <created>1750379349888</created>
      <option name="number" value="00006" />
      <option name="presentableId" value="LOCAL-00006" />
      <option name="project" value="LOCAL" />
      <updated>1750379349888</updated>
    </task>
    <task id="LOCAL-00007" summary="最后的修改">
      <option name="closed" value="true" />
      <created>1750379374431</created>
      <option name="number" value="00007" />
      <option name="presentableId" value="LOCAL-00007" />
      <option name="project" value="LOCAL" />
      <updated>1750379374431</updated>
    </task>
    <task id="LOCAL-00008" summary="最后的修改-1">
      <option name="closed" value="true" />
      <created>1750404748608</created>
      <option name="number" value="00008" />
      <option name="presentableId" value="LOCAL-00008" />
      <option name="project" value="LOCAL" />
      <updated>1750404748608</updated>
    </task>
    <task id="LOCAL-00009" summary="最后的修改-2">
      <option name="closed" value="true" />
      <created>1750409747632</created>
      <option name="number" value="00009" />
      <option name="presentableId" value="LOCAL-00009" />
      <option name="project" value="LOCAL" />
      <updated>1750409747632</updated>
    </task>
    <task id="LOCAL-00010" summary="最后的修改-2 当装备数量过多时，装备网格会超出物品选择器面板的底部边界，导致装备图标显示在面板外部或与底部信息栏重叠。">
      <option name="closed" value="true" />
      <created>1750414507497</created>
      <option name="number" value="00010" />
      <option name="presentableId" value="LOCAL-00010" />
      <option name="project" value="LOCAL" />
      <updated>1750414507497</updated>
    </task>
    <task id="LOCAL-00011" summary="请修改装备强化模拟器中的装备选择器工具提示（tooltip）的透明度设置，以提高可读性：">
      <option name="closed" value="true" />
      <created>1750415467542</created>
      <option name="number" value="00011" />
      <option name="presentableId" value="LOCAL-00011" />
      <option name="project" value="LOCAL" />
      <updated>1750415467542</updated>
    </task>
    <task id="LOCAL-00012" summary="1">
      <option name="closed" value="true" />
      <created>1750416911858</created>
      <option name="number" value="00012" />
      <option name="presentableId" value="LOCAL-00012" />
      <option name="project" value="LOCAL" />
      <updated>1750416911858</updated>
    </task>
    <task id="LOCAL-00013" summary="请修改装备强化模拟器的信息面板组件，在背景图片上添加两个复选框控件：">
      <option name="closed" value="true" />
      <created>1750433350737</created>
      <option name="number" value="00013" />
      <option name="presentableId" value="LOCAL-00013" />
      <option name="project" value="LOCAL" />
      <updated>1750433350737</updated>
    </task>
    <task id="LOCAL-00014" summary="字体">
      <option name="closed" value="true" />
      <created>1750435717226</created>
      <option name="number" value="00014" />
      <option name="presentableId" value="LOCAL-00014" />
      <option name="project" value="LOCAL" />
      <updated>1750435717226</updated>
    </task>
    <task id="LOCAL-00015" summary="项目说明文档">
      <option name="closed" value="true" />
      <created>1750436940426</created>
      <option name="number" value="00015" />
      <option name="presentableId" value="LOCAL-00015" />
      <option name="project" value="LOCAL" />
      <updated>1750436940426</updated>
    </task>
    <task id="LOCAL-00016" summary="项目说明文档">
      <option name="closed" value="true" />
      <created>1750437857465</created>
      <option name="number" value="00016" />
      <option name="presentableId" value="LOCAL-00016" />
      <option name="project" value="LOCAL" />
      <updated>1750437857465</updated>
    </task>
    <task id="LOCAL-00017" summary="项目说明文档">
      <option name="closed" value="true" />
      <created>1750442358536</created>
      <option name="number" value="00017" />
      <option name="presentableId" value="LOCAL-00017" />
      <option name="project" value="LOCAL" />
      <updated>1750442358536</updated>
    </task>
    <task id="LOCAL-00018" summary="项目说明文档">
      <option name="closed" value="true" />
      <created>1750444261206</created>
      <option name="number" value="00018" />
      <option name="presentableId" value="LOCAL-00018" />
      <option name="project" value="LOCAL" />
      <updated>1750444261206</updated>
    </task>
    <task id="LOCAL-00019" summary="项目说明文档">
      <option name="closed" value="true" />
      <created>1750444306413</created>
      <option name="number" value="00019" />
      <option name="presentableId" value="LOCAL-00019" />
      <option name="project" value="LOCAL" />
      <updated>1750444306413</updated>
    </task>
    <task id="LOCAL-00020" summary="项目说明文档">
      <option name="closed" value="true" />
      <created>1750444334822</created>
      <option name="number" value="00020" />
      <option name="presentableId" value="LOCAL-00020" />
      <option name="project" value="LOCAL" />
      <updated>1750444334822</updated>
    </task>
    <task id="LOCAL-00021" summary="集成API后端系统 ，第1个版本">
      <option name="closed" value="true" />
      <created>1750527363909</created>
      <option name="number" value="00021" />
      <option name="presentableId" value="LOCAL-00021" />
      <option name="project" value="LOCAL" />
      <updated>1750527363909</updated>
    </task>
    <task id="LOCAL-00022" summary="集成API后端系统 ，第1个版本-修改">
      <option name="closed" value="true" />
      <created>1750529873002</created>
      <option name="number" value="00022" />
      <option name="presentableId" value="LOCAL-00022" />
      <option name="project" value="LOCAL" />
      <updated>1750529873002</updated>
    </task>
    <task id="LOCAL-00023" summary="集成API后端系统 ，第1个版本-修改">
      <option name="closed" value="true" />
      <created>1750579433591</created>
      <option name="number" value="00023" />
      <option name="presentableId" value="LOCAL-00023" />
      <option name="project" value="LOCAL" />
      <updated>1750579433591</updated>
    </task>
    <task id="LOCAL-00024" summary="集成API后端系统 ，第1个版本-修改">
      <option name="closed" value="true" />
      <created>1750579484279</created>
      <option name="number" value="00024" />
      <option name="presentableId" value="LOCAL-00024" />
      <option name="project" value="LOCAL" />
      <updated>1750579484279</updated>
    </task>
    <task id="LOCAL-00025" summary="集成API后端系统 ，第1个版本-修改">
      <option name="closed" value="true" />
      <created>1750579798046</created>
      <option name="number" value="00025" />
      <option name="presentableId" value="LOCAL-00025" />
      <option name="project" value="LOCAL" />
      <updated>1750579798046</updated>
    </task>
    <task id="LOCAL-00026" summary="集成API后端系统 ，第1个版本-修改">
      <option name="closed" value="true" />
      <created>1750582351049</created>
      <option name="number" value="00026" />
      <option name="presentableId" value="LOCAL-00026" />
      <option name="project" value="LOCAL" />
      <updated>1750582351049</updated>
    </task>
    <task id="LOCAL-00027" summary="集成API后端系统 ，第1个版本-修改">
      <option name="closed" value="true" />
      <created>1750582354358</created>
      <option name="number" value="00027" />
      <option name="presentableId" value="LOCAL-00027" />
      <option name="project" value="LOCAL" />
      <updated>1750582354358</updated>
    </task>
    <task id="LOCAL-00028" summary="集成API后端系统 ，第1个版本-修改">
      <option name="closed" value="true" />
      <created>1750585748270</created>
      <option name="number" value="00028" />
      <option name="presentableId" value="LOCAL-00028" />
      <option name="project" value="LOCAL" />
      <updated>1750585748270</updated>
    </task>
    <task id="LOCAL-00029" summary="集成API后端系统 ，第1个版本-修改">
      <option name="closed" value="true" />
      <created>1750598446176</created>
      <option name="number" value="00029" />
      <option name="presentableId" value="LOCAL-00029" />
      <option name="project" value="LOCAL" />
      <updated>1750598446176</updated>
    </task>
    <task id="LOCAL-00030" summary="集成API后端系统 ，第2个版本-修改">
      <option name="closed" value="true" />
      <created>1750606601605</created>
      <option name="number" value="00030" />
      <option name="presentableId" value="LOCAL-00030" />
      <option name="project" value="LOCAL" />
      <updated>1750606601605</updated>
    </task>
    <task id="LOCAL-00031" summary="集成API后端系统 ，第2个版本-修改">
      <option name="closed" value="true" />
      <created>1750608342704</created>
      <option name="number" value="00031" />
      <option name="presentableId" value="LOCAL-00031" />
      <option name="project" value="LOCAL" />
      <updated>1750608342704</updated>
    </task>
    <task id="LOCAL-00032" summary="集成API后端系统 ，第2个版本-修改">
      <option name="closed" value="true" />
      <created>1750611925026</created>
      <option name="number" value="00032" />
      <option name="presentableId" value="LOCAL-00032" />
      <option name="project" value="LOCAL" />
      <updated>1750611925026</updated>
    </task>
    <task id="LOCAL-00033" summary="集成API后端系统 ，第2个版本-修改">
      <option name="closed" value="true" />
      <created>1750612762399</created>
      <option name="number" value="00033" />
      <option name="presentableId" value="LOCAL-00033" />
      <option name="project" value="LOCAL" />
      <updated>1750612762399</updated>
    </task>
    <task id="LOCAL-00034" summary="优化冒险岛情报站的页面布局和导航结构">
      <option name="closed" value="true" />
      <created>1750614189267</created>
      <option name="number" value="00034" />
      <option name="presentableId" value="LOCAL-00034" />
      <option name="project" value="LOCAL" />
      <updated>1750614189267</updated>
    </task>
    <task id="LOCAL-00035" summary="优化冒险岛情报站的页面布局和导航结构">
      <option name="closed" value="true" />
      <created>1750614393748</created>
      <option name="number" value="00035" />
      <option name="presentableId" value="LOCAL-00035" />
      <option name="project" value="LOCAL" />
      <updated>1750614393748</updated>
    </task>
    <task id="LOCAL-00036" summary="优化，注册登录布局">
      <option name="closed" value="true" />
      <created>1750614473946</created>
      <option name="number" value="00036" />
      <option name="presentableId" value="LOCAL-00036" />
      <option name="project" value="LOCAL" />
      <updated>1750614473946</updated>
    </task>
    <task id="LOCAL-00037" summary="11">
      <option name="closed" value="true" />
      <created>1750650620249</created>
      <option name="number" value="00037" />
      <option name="presentableId" value="LOCAL-00037" />
      <option name="project" value="LOCAL" />
      <updated>1750650620249</updated>
    </task>
    <task id="LOCAL-00038" summary="添加装备强化API接口">
      <option name="closed" value="true" />
      <created>1750665390220</created>
      <option name="number" value="00038" />
      <option name="presentableId" value="LOCAL-00038" />
      <option name="project" value="LOCAL" />
      <updated>1750665390220</updated>
    </task>
    <task id="LOCAL-00039" summary="添加装备强化API接口">
      <option name="closed" value="true" />
      <created>1750672260515</created>
      <option name="number" value="00039" />
      <option name="presentableId" value="LOCAL-00039" />
      <option name="project" value="LOCAL" />
      <updated>1750672260515</updated>
    </task>
    <task id="LOCAL-00040" summary="测试">
      <option name="closed" value="true" />
      <created>1750681731936</created>
      <option name="number" value="00040" />
      <option name="presentableId" value="LOCAL-00040" />
      <option name="project" value="LOCAL" />
      <updated>1750681731936</updated>
    </task>
    <task id="LOCAL-00041" summary="测试">
      <option name="closed" value="true" />
      <created>1750685068054</created>
      <option name="number" value="00041" />
      <option name="presentableId" value="LOCAL-00041" />
      <option name="project" value="LOCAL" />
      <updated>1750685068054</updated>
    </task>
    <task id="LOCAL-00042" summary="部署">
      <option name="closed" value="true" />
      <created>1750692127529</created>
      <option name="number" value="00042" />
      <option name="presentableId" value="LOCAL-00042" />
      <option name="project" value="LOCAL" />
      <updated>1750692127529</updated>
    </task>
    <task id="LOCAL-00043" summary="部署">
      <option name="closed" value="true" />
      <created>1750693652208</created>
      <option name="number" value="00043" />
      <option name="presentableId" value="LOCAL-00043" />
      <option name="project" value="LOCAL" />
      <updated>1750693652208</updated>
    </task>
    <task id="LOCAL-00044" summary="部署">
      <option name="closed" value="true" />
      <created>1750693792295</created>
      <option name="number" value="00044" />
      <option name="presentableId" value="LOCAL-00044" />
      <option name="project" value="LOCAL" />
      <updated>1750693792295</updated>
    </task>
    <task id="LOCAL-00045" summary="部署">
      <option name="closed" value="true" />
      <created>1750694971503</created>
      <option name="number" value="00045" />
      <option name="presentableId" value="LOCAL-00045" />
      <option name="project" value="LOCAL" />
      <updated>1750694971503</updated>
    </task>
    <task id="LOCAL-00046" summary="部署">
      <option name="closed" value="true" />
      <created>1750699745577</created>
      <option name="number" value="00046" />
      <option name="presentableId" value="LOCAL-00046" />
      <option name="project" value="LOCAL" />
      <updated>1750699745577</updated>
    </task>
    <task id="LOCAL-00047" summary="部署">
      <option name="closed" value="true" />
      <created>1750699991791</created>
      <option name="number" value="00047" />
      <option name="presentableId" value="LOCAL-00047" />
      <option name="project" value="LOCAL" />
      <updated>1750699991791</updated>
    </task>
    <task id="LOCAL-00048" summary="部署">
      <option name="closed" value="true" />
      <created>1750728133369</created>
      <option name="number" value="00048" />
      <option name="presentableId" value="LOCAL-00048" />
      <option name="project" value="LOCAL" />
      <updated>1750728133369</updated>
    </task>
    <task id="LOCAL-00049" summary="部署">
      <option name="closed" value="true" />
      <created>1750729256211</created>
      <option name="number" value="00049" />
      <option name="presentableId" value="LOCAL-00049" />
      <option name="project" value="LOCAL" />
      <updated>1750729256211</updated>
    </task>
    <task id="LOCAL-00050" summary="部署">
      <option name="closed" value="true" />
      <created>1750731548191</created>
      <option name="number" value="00050" />
      <option name="presentableId" value="LOCAL-00050" />
      <option name="project" value="LOCAL" />
      <updated>1750731548191</updated>
    </task>
    <task id="LOCAL-00051" summary="部署">
      <option name="closed" value="true" />
      <created>1750737321660</created>
      <option name="number" value="00051" />
      <option name="presentableId" value="LOCAL-00051" />
      <option name="project" value="LOCAL" />
      <updated>1750737321660</updated>
    </task>
    <option name="localTasksCounter" value="52" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="HEAD" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
            </State>
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="12" />
    <MESSAGE value="最后的修改" />
    <MESSAGE value="最后的修改-1" />
    <MESSAGE value="最后的修改-2" />
    <MESSAGE value="最后的修改-2 当装备数量过多时，装备网格会超出物品选择器面板的底部边界，导致装备图标显示在面板外部或与底部信息栏重叠。" />
    <MESSAGE value="请修改装备强化模拟器中的装备选择器工具提示（tooltip）的透明度设置，以提高可读性：" />
    <MESSAGE value="1" />
    <MESSAGE value="请修改装备强化模拟器的信息面板组件，在背景图片上添加两个复选框控件：" />
    <MESSAGE value="字体" />
    <MESSAGE value="项目说明文档" />
    <MESSAGE value="集成API后端系统 ，第1个版本" />
    <MESSAGE value="集成API后端系统 ，第1个版本-修改" />
    <MESSAGE value="集成API后端系统 ，第2个版本-修改" />
    <MESSAGE value="优化冒险岛情报站的页面布局和导航结构" />
    <MESSAGE value="优化，注册登录布局" />
    <MESSAGE value="11" />
    <MESSAGE value="添加装备强化API接口" />
    <MESSAGE value="测试" />
    <MESSAGE value="部署" />
    <option name="LAST_COMMIT_MESSAGE" value="部署" />
  </component>
</project>